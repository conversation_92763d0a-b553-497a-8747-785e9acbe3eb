const axios = require("axios");
const oauth = require("oauth-1.0a");
const crypto = require("crypto");

// Configuration for OAuth 1.0
const oauthConfig = {
  consumerKey:
    "9b15181920ab82f6be051f607eeb28d8b45f84840ea72626df882ca7fcde9f31",
  consumerSecret:
    "f503cb3e9d626d2b6611e6ce743f23a1f9719a8fe69ca37ee678df4432a28c87",
  accessToken:
    "cb535ffcc2589496acb19f2eec0cc630378d6828590d8e4f0161fc4b7f8e598c",
  accessTokenSecret:
    "c9ab57532db14ac4ac11531f4cd878e77f697d8465a145856fddd29721df7763",
  realm: "3667364",
};

const filesPrefixUrl = "https://tms-hul-media-demo.wify.co.in/";

const logAxiosError = (error) => {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    console.log(error.response.data);
    console.log(error.response.status);
    console.log(error.response.headers);
  } else if (error.request) {
    // The request was made but no response was received
    // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
    // http.ClientRequest in node.js
    console.log(error.request);
  } else {
    // Something happened in setting up the request that triggered an Error
    console.log("Error", error.message);
  }
  // console.log(error.config);
};

// console.log('request_data["headers"] ', request_data);
const makeRequest = async (data, requestType = "netsuite") => {
  if (requestType === "salesforce") {
    // Salesforce API request with Bearer token
    const salesforceUrl =
      "https://aosmithpureitq--aosqas001.sandbox.my.salesforce.com/services/apexrest/TMSIndiaCaseStartJob";
    const bearerToken =
      "00Ddm000005NsT3!AQEAQJvO0S8YHGR4TnvG0Q2vIGqUdD8jIhquFLFxpdn34POpAPg6jgyvfliF_78ss7ynobRGgcNeHMPt6mwn2hjEn98.9QPG";

    const headers = {
      Authorization: `Bearer ${bearerToken}`,
      "Content-Type": "application/json",
      Cookie:
        "BrowserId=6cQ6rd1TEe-u_MM-5GGxMQ; CookieConsentPolicy=0:1; LSKey-c$CookieConsentPolicy=0:1",
    };

    return await axios.post(salesforceUrl, data, { headers });
  } else {
    // Original NetSuite OAuth 1.0 request
    // Create an OAuth 1.0 instance
    const oauthInstance = oauth({
      consumer: {
        key: oauthConfig.consumerKey,
        secret: oauthConfig.consumerSecret,
        realm: oauthConfig.realm,
      },
      signature_method: "HMAC-SHA256",
      hash_function(baseString, key) {
        return crypto
          .createHmac("SHA256", key)
          .update(baseString)
          .digest("base64");
      },
      realm: oauthConfig.realm,
    });

    const token = {
      key: oauthConfig.accessToken,
      secret: oauthConfig.accessTokenSecret,
    };
    const request_data = {
      url: "https://3667364.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=2381&deploy=1",
      method: "POST",
      maxBodyLength: Infinity,
      body: JSON.stringify(data),
    };
    // Generate OAuth 1.0 headers
    const oauthHeaders = oauthInstance.toHeader(
      oauthInstance.authorize(request_data, token)
    );
    const headers = {
      "Content-Type": "application/json",
      ...oauthHeaders,
    };
    return await axios.post(request_data.url, JSON.stringify(data), {
      headers,
    });
  }
};

const convertAttachmentToPublicUrl = (internalUrl) => {
  if (internalUrl) {
    const string = internalUrl; //"org_14/capture_image_1124406008470300_uaJ1IeWPIghOVE60.png";
    return `${filesPrefixUrl}${string}`;
  } else {
    return "";
  }
};

const handler = async (event) => {
  let responseStatus = false;
  let responseMessage = "Error please contact admin";

  console.log("event form_data", event.form_data);
  console.log("event request_data", event.request_data);

  const fieldVsKey = {
    netsuiteOrderId: "79a88c7b-c64f-46c4-a277-bc80efa1c154",
    autoScannedBarcodeId: "8aefeefc-e0dd-45bf-8003-7870a8963156",
    manualEnteredBarcodeId: "f7b8d82d-faf5-488e-bb49-a2c67605c56a",
    beforeImageId: "84fc9160-eb45-4e93-ac7d-42a5cd96e5d1",
    barcodeImage: "49f14758-e06b-42bb-ba6d-175e71dffa9a",
  };
  const netsuiteOrderId = event.request_data[fieldVsKey.netsuiteOrderId];
  // console.log('netsuiteOrderId',netsuiteOrderId)
  const autoScannedBarcode = event.form_data[fieldVsKey.autoScannedBarcodeId];
  const manualEnteredBarcode =
    event.form_data[fieldVsKey.manualEnteredBarcodeId];
  const beforeImage = convertAttachmentToPublicUrl(
    event.form_data.attachments[fieldVsKey.beforeImageId]?.[0]
  );
  const barcodeImage = convertAttachmentToPublicUrl(
    event.form_data.attachments[fieldVsKey.barcodeImage]?.[0]
  );
  const attachments = event.form_data.attachments?.general || [];
  const finalAttachments = attachments.map((url) => filesPrefixUrl + url);

  console.log("autoScannedBarcode", autoScannedBarcode);
  console.log("manualEnteredBarcode", manualEnteredBarcode);

  let data = {
    SeriesAndSerialCombination: autoScannedBarcode || manualEnteredBarcode,
    isScanned: autoScannedBarcode ? "T" : "F", // "T" when scanned, F when manually entered,
    case_number: netsuiteOrderId,
    BeforeInvoiceUrl: beforeImage,
    custrecord_before_image_tms: beforeImage,
    barcode_img: barcodeImage,
    Start_job_attachments: finalAttachments,
  };

  console.log("Netsuite api call data", data);
  try {
    const response = await makeRequest(data);
    console.log("Netsuite api response", response.data); // Handle the API response data

    const netSuiteData = response.data.data?.[0] || response.data;

    if (netSuiteData && netSuiteData.message) {
      let { message, responseCode, status } = netSuiteData;
      responseMessage = message;
      console.log(
        "message,responseCode,status : ",
        message,
        responseCode,
        status
      );
      if (status == "Success") {
        responseStatus = true; //
      } else {
        responseMessage = message;
      }
    } else {
      // this is a fatal error from netsuite
      // the response is not as per documentation rxd
      responseMessage = JSON.stringify(response.data);
    }
  } catch (error) {
    logAxiosError(error);
    // handle error
  }

  if (autoScannedBarcode == undefined && manualEnteredBarcode == undefined) {
    responseMessage =
      "You have already updated this status, you cannot update it again.";
  }

  // if(data.remarks)
  // TODO implement
  const response = {
    status: responseStatus,
    message: responseMessage,
  };
  return response;
};

// handler({});

exports.handler = handler;
